<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据结构调试工具</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .upload-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            text-align: center;
        }
        .file-input {
            margin: 10px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .log-section {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            max-height: 500px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .data-preview {
            margin-top: 20px;
            padding: 15px;
            background: #e9ecef;
            border-radius: 4px;
            max-height: 300px;
            overflow: auto;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>菜篮子比价工具 - 数据结构调试</h1>
        
        <div class="upload-section">
            <h3>上传Excel文件</h3>
            <div>
                <label>销售价目表：</label>
                <input type="file" id="salesFile" class="file-input" accept=".xlsx,.xls">
            </div>
            <div>
                <label>菜篮子价格表：</label>
                <input type="file" id="basketFile" class="file-input" accept=".xlsx,.xls">
            </div>
            <button class="btn" onclick="analyzeData()">分析数据结构</button>
            <button class="btn" onclick="testMatching()">测试商品匹配</button>
            <button class="btn" onclick="clearLog()">清空日志</button>
        </div>

        <div id="logSection" class="log-section"></div>
        
        <div id="dataPreview" class="data-preview" style="display: none;">
            <h3>数据预览</h3>
            <div id="previewContent"></div>
        </div>
    </div>

    <script>
        let salesData = null;
        let basketData = null;

        function log(message) {
            const logSection = document.getElementById('logSection');
            const timestamp = new Date().toLocaleTimeString();
            logSection.textContent += `[${timestamp}] ${message}\n`;
            logSection.scrollTop = logSection.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('logSection').textContent = '';
        }

        // 文件上传处理
        document.getElementById('salesFile').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                log(`正在读取销售价目表: ${file.name}`);
                readExcelFile(file, (data) => {
                    salesData = data;
                    log(`销售价目表读取完成，共 ${data.length} 行数据`);
                    showDataPreview('销售价目表', data);
                });
            }
        });

        document.getElementById('basketFile').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                log(`正在读取菜篮子价格表: ${file.name}`);
                readExcelFile(file, (data) => {
                    basketData = data;
                    log(`菜篮子价格表读取完成，共 ${data.length} 行数据`);
                    showDataPreview('菜篮子价格表', data);
                });
            }
        });

        function readExcelFile(file, callback) {
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, { type: 'array' });
                    const firstSheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[firstSheetName];
                    const jsonData = XLSX.utils.sheet_to_json(worksheet);
                    callback(jsonData);
                } catch (error) {
                    log(`读取文件失败: ${error.message}`);
                }
            };
            reader.readAsArrayBuffer(file);
        }

        function showDataPreview(title, data) {
            const previewSection = document.getElementById('dataPreview');
            const previewContent = document.getElementById('previewContent');
            
            if (data && data.length > 0) {
                const columns = Object.keys(data[0]);
                let html = `<h4>${title} (前5行)</h4>`;
                html += '<table><thead><tr>';
                columns.forEach(col => {
                    html += `<th>${col}</th>`;
                });
                html += '</tr></thead><tbody>';
                
                data.slice(0, 5).forEach(row => {
                    html += '<tr>';
                    columns.forEach(col => {
                        html += `<td>${row[col] || ''}</td>`;
                    });
                    html += '</tr>';
                });
                html += '</tbody></table>';
                
                previewContent.innerHTML = html;
                previewSection.style.display = 'block';
            }
        }

        function analyzeData() {
            if (!salesData || !basketData) {
                log('❌ 请先上传两个Excel文件');
                return;
            }

            log('🔍 开始分析数据结构...');
            
            // 分析销售数据
            log('\n=== 销售价目表分析 ===');
            const salesColumns = Object.keys(salesData[0]);
            log(`列名: ${salesColumns.join(', ')}`);
            log(`数据行数: ${salesData.length}`);
            log('前3行数据:');
            salesData.slice(0, 3).forEach((row, index) => {
                log(`  行${index + 1}: ${JSON.stringify(row)}`);
            });

            // 分析菜篮子数据
            log('\n=== 菜篮子价格表分析 ===');
            const basketColumns = Object.keys(basketData[0]);
            log(`列名: ${basketColumns.join(', ')}`);
            log(`数据行数: ${basketData.length}`);
            log('前3行数据:');
            basketData.slice(0, 3).forEach((row, index) => {
                log(`  行${index + 1}: ${JSON.stringify(row)}`);
            });

            // 查找关键列
            log('\n=== 关键列识别 ===');
            
            // 销售数据关键列
            const salesProductCol = findColumn(salesColumns, ['物料名称', '商品名称', '产品名称', '名称']);
            const salesUnitCol = findColumn(salesColumns, ['计价单位', '单位', '规格']);
            const salesPriceCol = findColumn(salesColumns, ['市场价', '价格', '单价', '售价']);
            
            log(`销售数据 - 商品名称列: ${salesProductCol}`);
            log(`销售数据 - 单位列: ${salesUnitCol}`);
            log(`销售数据 - 价格列: ${salesPriceCol}`);

            // 菜篮子数据关键列
            const basketProductCol = findColumn(basketColumns, ['商品/分类名称', '商品名称', '分类名称', '名称', '商品', '品名']) || basketColumns[0];
            const basketUnitCol = findColumn(basketColumns, ['计价单位', '单位', '规格', '计量单位']);
            
            log(`菜篮子数据 - 商品名称列: ${basketProductCol}`);
            log(`菜篮子数据 - 单位列: ${basketUnitCol}`);

            // 查找日期列
            log('\n=== 日期列分析 ===');
            const dateColumns = findDateColumns(basketColumns);
            log(`找到 ${dateColumns.length} 个可能的价格列:`);
            dateColumns.forEach(col => {
                log(`  ${col.column} (索引: ${col.columnIndex}, 日期: ${col.day})`);
                // 显示该列的前几个值
                log(`    前3个值: ${basketData.slice(0, 3).map(row => `"${row[col.column]}"`).join(', ')}`);
            });
        }

        function findColumn(columns, possibleNames) {
            for (const name of possibleNames) {
                const found = columns.find(col => col && col.includes(name));
                if (found) return found;
            }
            return null;
        }

        function findDateColumns(columns) {
            const dateColumns = [];
            const datePattern = /(\d{1,2})[日号]/;
            const monthPattern = /(\d{1,2})月(\d{1,2})[日号]/;
            const fullDatePattern = /(\d{4})年(\d{1,2})月(\d{1,2})日/;

            for (let i = 0; i < columns.length; i++) {
                const col = columns[i];
                if (!col) continue;

                // 优先查找包含"本期"的日期列
                if (datePattern.test(col) && col.includes('本期')) {
                    const match = col.match(datePattern);
                    if (match) {
                        dateColumns.push({
                            column: col,
                            columnIndex: i,
                            day: parseInt(match[1]),
                            priority: getDatePriority(parseInt(match[1]))
                        });
                    }
                }
                // 查找完整日期格式
                else if (fullDatePattern.test(col)) {
                    const match = col.match(fullDatePattern);
                    if (match) {
                        const day = parseInt(match[3]);
                        dateColumns.push({
                            column: col,
                            columnIndex: i,
                            day: day,
                            year: parseInt(match[1]),
                            month: parseInt(match[2]),
                            priority: getDatePriority(day)
                        });
                    }
                }
                // 查找月日格式
                else if (monthPattern.test(col)) {
                    const match = col.match(monthPattern);
                    if (match) {
                        dateColumns.push({
                            column: col,
                            columnIndex: i,
                            day: parseInt(match[2]),
                            month: parseInt(match[1]),
                            priority: getDatePriority(parseInt(match[2]))
                        });
                    }
                }
                // 查找其他价格列
                else if (col.includes('价格') || col.includes('单价') || col.includes('元') || /\d+[日号]/.test(col)) {
                    const match = col.match(datePattern);
                    const day = match ? parseInt(match[1]) : 25;
                    dateColumns.push({
                        column: col,
                        columnIndex: i,
                        day: day,
                        priority: getDatePriority(day)
                    });
                }
            }

            return dateColumns.sort((a, b) => a.priority - b.priority);
        }

        function getDatePriority(day) {
            const targetDay = 25;
            if (day === targetDay) return 0;
            const distance = Math.abs(day - targetDay);
            const bonus = day > targetDay ? 0 : 0.1;
            return distance + bonus;
        }

        function testMatching() {
            if (!salesData || !basketData) {
                log('❌ 请先上传两个Excel文件并分析数据结构');
                return;
            }

            log('\n🧪 开始测试商品匹配...');
            
            // 获取数据结构
            const salesColumns = Object.keys(salesData[0]);
            const basketColumns = Object.keys(basketData[0]);
            
            const salesProductCol = findColumn(salesColumns, ['物料名称', '商品名称', '产品名称', '名称']);
            const basketProductCol = findColumn(basketColumns, ['商品/分类名称', '商品名称', '分类名称', '名称', '商品', '品名']) || basketColumns[0];
            const dateColumns = findDateColumns(basketColumns);
            
            if (!salesProductCol || !basketProductCol || dateColumns.length === 0) {
                log('❌ 缺少必要的列信息');
                return;
            }

            const bestDateColumn = dateColumns[0].column;
            log(`使用价格列: ${bestDateColumn}`);

            // 测试前5个销售商品的匹配
            log('\n=== 匹配测试结果 ===');
            salesData.slice(0, 5).forEach((salesItem, index) => {
                const productName = salesItem[salesProductCol];
                log(`\n测试商品 ${index + 1}: "${productName}"`);
                
                // 精确匹配
                const exactMatch = basketData.find(item => {
                    const basketProduct = item[basketProductCol];
                    return basketProduct === productName;
                });
                
                if (exactMatch) {
                    const priceValue = exactMatch[bestDateColumn];
                    log(`  ✓ 精确匹配: "${exactMatch[basketProductCol]}" - 价格: "${priceValue}"`);
                } else {
                    log(`  ✗ 精确匹配失败`);
                    
                    // 包含匹配
                    const containsMatch = basketData.find(item => {
                        const basketProduct = item[basketProductCol];
                        return basketProduct && (basketProduct.includes(productName) || productName.includes(basketProduct));
                    });
                    
                    if (containsMatch) {
                        const priceValue = containsMatch[bestDateColumn];
                        log(`  ✓ 包含匹配: "${containsMatch[basketProductCol]}" - 价格: "${priceValue}"`);
                    } else {
                        log(`  ✗ 包含匹配失败`);
                        log(`  可用的菜篮子商品名称样本:`);
                        basketData.slice(0, 5).forEach(item => {
                            log(`    "${item[basketProductCol]}"`);
                        });
                    }
                }
            });
        }
    </script>
</body>
</html>
